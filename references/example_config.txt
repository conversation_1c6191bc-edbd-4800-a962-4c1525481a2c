# JFrog Enterprise CLI Configuration - Complete Ecosystem Support
# This configuration demonstrates SLSA Level 4 compliance and full package ecosystem integration

# Default server for operations
default: "production"

# Global settings for enterprise operations
global:
  log_level: "info"                    # debug, info, warn, error
  parallel: 16                         # Increased for enterprise workloads
  retries: 5                          # Higher retry count for reliability
  retry_delay: "3s"                   # Longer delay for enterprise networks
  progress_bars: true                 # Visual feedback for long operations
  color_output: true                  # Enhanced user experience

# Multi-environment server configurations with complete ecosystem support
servers:
  # Production environment with SLSA Level 4 enforcement
  production:
    url: "https://artifactory.enterprise.com"
    access_token: "${JFROG_PROD_TOKEN}"
    refresh_token: "${JFROG_PROD_REFRESH_TOKEN}"
    timeout: "60s"                     # Longer timeout for enterprise
    insecure_tls: false
    distribution_endpoint: "https://distribution.enterprise.com"
    xray_endpoint: "https://xray.enterprise.com"
    headers:
      User-Agent: "enterprise-cli/3.0.0"
      X-Environment: "production"
      X-SLSA-Level: "4"
      X-Security-Context: "enterprise"
    repositories:
      maven:
        - "maven-central-prod"
        - "maven-releases-prod"
        - "maven-snapshots-prod"
      npm:
        - "npm-registry-prod"
        - "npm-virtual-prod"
        - "npm-remote-prod"
      docker:
        - "docker-registry-prod"
        - "docker-virtual-prod"
        - "docker-remote-prod"
      go:
        - "go-modules-prod"
        - "go-virtual-prod"
        - "goproxy-remote-prod"
      pypi:
        - "pypi-registry-prod"
        - "pypi-virtual-prod"
        - "pypi-remote-prod"
      rust:                            # Rust/Cargo repositories
        - "cargo-registry-prod"
        - "cargo-virtual-prod"
        - "cargo-remote-prod"
      helm:
        - "helm-charts-prod"
        - "helm-virtual-prod"
        - "helm-remote-prod"
      oci:                             # ORAS artifacts
        - "oci-artifacts-prod"
        - "oci-virtual-prod"
      porter:                          # Porter CNAB bundles
        - "porter-bundles-prod"
        - "porter-mixins-prod"
      plugins:                         # Plugin distribution
        - "cli-plugins-prod"
        - "custom-plugins-prod"
      generic:
        - "generic-artifacts-prod"
        - "binaries-prod"
        - "releases-prod"

  # Development environment with relaxed SLSA requirements
  development:
    url: "https://artifactory-dev.enterprise.com"
    username: "${JFROG_DEV_USER}"
    password: "${JFROG_DEV_PASS}"
    timeout: "30s"
    insecure_tls: false
    distribution_endpoint: "https://distribution-dev.enterprise.com"
    xray_endpoint: "https://xray-dev.enterprise.com"
    repositories:
      maven: ["maven-dev", "maven-snapshots-dev"]
      npm: ["npm-dev", "npm-virtual-dev"]
      docker: ["docker-dev", "docker-virtual-dev"]
      go: ["go-dev", "go-virtual-dev"]
      pypi: ["pypi-dev", "pypi-virtual-dev"]
      rust: ["cargo-dev", "cargo-virtual-dev"]
      helm: ["helm-dev"]
      oci: ["oci-dev"]
      porter: ["porter-dev"]
      plugins: ["plugins-dev"]
      generic: ["generic-dev"]

  # Cloud SaaS environment with multi-region support
  saas:
    url: "https://enterprise.jfrog.io"
    access_token: "${JFROG_SAAS_TOKEN}"
    refresh_token: "${JFROG_SAAS_REFRESH_TOKEN}"
    timeout: "45s"
    insecure_tls: false
    distribution_endpoint: "https://enterprise.jfrog.io/distribution"
    xray_endpoint: "https://enterprise.jfrog.io/xray"
    headers:
      X-JFrog-Art-Api: "${JFROG_SAAS_API_KEY}"
      X-Region: "us-east-1"
    repositories:
      maven: ["enterprise-maven", "maven-central-cache", "maven-virtual"]
      npm: ["enterprise-npm", "npmjs-cache", "npm-virtual"]
      docker: ["enterprise-docker", "docker-hub-cache", "docker-virtual"]
      go: ["enterprise-go", "goproxy-cache", "go-virtual"]
      pypi: ["enterprise-pypi", "pypi-cache", "pypi-virtual"]
      rust: ["enterprise-cargo", "crates-io-cache", "cargo-virtual"]
      helm: ["enterprise-helm", "helm-hub-cache", "helm-virtual"]
      oci: ["enterprise-oci", "oci-virtual"]
      porter: ["enterprise-porter", "porter-virtual"]
      plugins: ["enterprise-plugins", "plugins-virtual"]

# Enhanced package configurations with full ecosystem support
package_configs:
  maven:
    default_group_id: "com.enterprise"
    snapshot_policy: "unique"
    checksum_policy: "generate-if-absent"
    handle_releases: true
    handle_snapshots: true
    slsa_required: true
    sign_artifacts: true
    
  npm:
    default_scope: "@enterprise"
    always_auth: true
    registry_url_suffix: "/api/npm"
    enable_audit: true
    slsa_required: true
    provenance_required: true
    
  docker:
    default_namespace: "enterprise"
    enable_token_authentication: true
    max_unique_tags: 10
    enable_content_trust: true
    cosign_verification: true
    slsa_attestation: true
    
  go:
    goproxy_url_suffix: "/api/go"
    enable_checksum_verification: true
    enable_sumdb: true
    slsa_required: true
    
  pypi:
    repository_url_suffix: "/api/pypi"
    enable_ssl_verification: true
    slsa_required: true
    signature_verification: true
    
  # Rust/Cargo configuration
  rust:
    default_crate_name: "enterprise-crate"
    registry_url: "https://artifactory.enterprise.com/artifactory/api/cargo/cargo-registry-prod"
    index_url: "https://artifactory.enterprise.com/artifactory/cargo-registry-prod.git"
    enable_sparse_index: true
    cargo_config_path: "~/.cargo/config.toml"
    allowed_licenses:
      - "MIT"
      - "Apache-2.0"
      - "BSD-3-Clause"
      - "ISC"
    banned_licenses:
      - "GPL-3.0"
      - "AGPL-3.0"
      - "LGPL-3.0"
    required_features:
      - "security-audit"
      - "slsa-provenance"
    slsa_required: true
    cargo_audit: true
    
  # Helm configuration
  helm:
    default_namespace: "enterprise"
    enable_oci: true
    chart_testing: true
    security_scanning: true
    slsa_required: true

# SLSA Level 4 compliance configuration
slsa:
  level: 4                             # Enforce SLSA Level 4
  require_provenance: true             # Mandatory provenance
  verify_signatures: true              # Verify all signatures
  hermetic_builds: true                # Hermetic build environments
  two_party_review: true               # Two-party approval required
  isolated_builds: true                # Isolated build environments
  parameterized_triggers: true         # Parameterized build triggers
  ephemeral_environments: true         # Ephemeral build environments
  provenance_format: "slsa"            # SLSA v1.0 format
  builder_identities:
    - "https://github.com/enterprise/actions"
    - "https://dagger.io/enterprise-builder"
    - "https://jenkins.enterprise.com/builder"
  allowed_sources:
    - "github.com/enterprise/*"
    - "gitlab.enterprise.com/*"
  required_reviewers: 2                # Minimum two reviewers
  enable_transparency_log: true        # Rekor transparency log
  cosign_integration: true             # Cosign signature verification

# Security and compliance configuration
security:
  # Cosign configuration for container signing
  cosign:
    enabled: true
    keyless_mode: true                 # Use Fulcio/Rekor for keyless signing
    fulcio_url: "https://fulcio.sigstore.dev"
    rekor_url: "https://rekor.sigstore.dev"
    tsa_url: "https://tsa.sigstore.dev"
    private_key_path: "${COSIGN_PRIVATE_KEY_PATH}"
    password: "${COSIGN_PASSWORD}"
    
  # In-toto attestation configuration
  in_toto:
    enabled: true
    layout_path: "/etc/in-toto/layout.json"
    public_keys_path: "/etc/in-toto/keys"
    functionary_keys:
      - "build-key.pub"
      - "test-key.pub"
      - "deploy-key.pub"
    
  # Rekor transparency log configuration
  rekor:
    enabled: true
    url: "https://rekor.sigstore.dev"
    tree_id: "8932273441526131678"
    verify_inclusion: true
    
  # Vulnerability database configuration
  vulnerability_db:
    enabled: true
    sources:
      - "NVD"
      - "WhiteSource"
      - "JFrog"
      - "GitHub"
      - "OSV"
    update_interval: "1h"
    fail_on_high: true
    fail_on_critical: true
    
  # Policy engine configuration
  policy_engine:
    enabled: true
    engine: "opa"                      # Open Policy Agent
    policies_path: "/etc/policies"
    default_action: "deny"
    audit_mode: false

# Porter CNAB configuration
porter:
  enabled: true
  default_namespace: "enterprise"
  bundle_repository: "porter-bundles-prod"
  mixin_repository: "porter-mixins-prod"
  credentials:
    azure: "${AZURE_CREDENTIALS}"
    aws: "${AWS_CREDENTIALS}"
    gcp: "${GCP_CREDENTIALS}"
    kubernetes: "${KUBE_CONFIG}"
  default_mixins:
    - "helm3"
    - "kubernetes"
    - "terraform"
    - "az"
    - "aws"
    - "gcloud"
  custom_mixins:
    - name: "enterprise-vault"
      repository: "cli-plugins-prod"
      version: "1.0.0"
      source: "repository"
    - name: "compliance-checker"
      repository: "cli-plugins-prod"
      version: "2.1.0"
      source: "repository"

# Plugin distribution configuration
plugins:
  enabled: true
  plugin_dir: "~/.jfrog-enterprise/plugins"
  repository: "cli-plugins-prod"
  auto_update: true
  plugins:
    security-scanner:
      version: "3.2.1"
      repository: "cli-plugins-prod"
      checksum: "sha256:abcd1234..."
      signature: "cosign-signature"
      enabled: true
      config:
        scan_on_upload: "true"
        fail_on_critical: "true"
    compliance-checker:
      version: "2.1.0"
      repository: "cli-plugins-prod"
      checksum: "sha256:efgh5678..."
      signature: "cosign-signature"
      enabled: true
      config:
        slsa_level: "4"
        require_attestations: "true"
    performance-monitor:
      version: "1.0.5"
      repository: "cli-plugins-prod"
      checksum: "sha256:ijkl9012..."
      enabled: false
  security:
    verify_signatures: true            # Verify plugin signatures
    allowed_publishers:
      - "enterprise-plugins"
      - "certified-vendors"
    quarantine_unsigned: true          # Quarantine unsigned plugins
    scan_plugins: true                 # Scan plugins for vulnerabilities

# Dagger CI/CD configuration
dagger:
  enabled: true
  engine: "docker"                     # docker, podman, containerd
  registry: "docker-registry-prod"     # For caching Dagger modules
  module_cache: "~/.dagger/cache"
  parallelism: 8                       # Parallel Dagger operations
  secrets:
    registry_token: "${DOCKER_REGISTRY_TOKEN}"
    build_secrets: "${BUILD_SECRETS}"
    signing_key: "${SIGNING_KEY}"
  slsa_compliance: true                # Enable SLSA compliance features
  hermetic_builds: true                # Force hermetic builds
  provenance_generation: true          # Generate build provenance

# Release Bundle v2 configuration
release_bundles:
  enabled: true
  default_distribution: "enterprise-distribution"
  signing_key: "${RELEASE_BUNDLE_SIGNING_KEY}"
  verification_keys:
    - "${RELEASE_BUNDLE_VERIFICATION_KEY_1}"
    - "${RELEASE_BUNDLE_VERIFICATION_KEY_2}"
  auto_sign: true
  gpg_passphrase: "${GPG_PASSPHRASE}"
  distribution_rules:
    - name: "production-sites"
      sites:
        - "us-east-prod"
        - "us-west-prod"
        - "eu-west-prod"
        - "ap-south-prod"
      repositories:
        - "maven-releases-prod"
        - "docker-registry-prod"
        - "npm-registry-prod"
      auto_deploy: true
    - name: "staging-sites"
      sites:
        - "us-east-staging"
        - "eu-west-staging"
      repositories:
        - "maven-snapshots-prod"
        - "docker-dev"
      auto_deploy: false
    - name: "edge-locations"
      sites:
        - "edge-us-1"
        - "edge-eu-1"
        - "edge-ap-1"
      repositories:
        - "oci-artifacts-prod"
        - "generic-artifacts-prod"
      auto_deploy: true

# Build integration with enterprise CI/CD systems
build_integration:
  # Jenkins enterprise integration
  jenkins:
    enabled: true
    build_info_url: "https://jenkins.enterprise.com"
    capture_env_vars: true
    slsa_integration: true             # Generate SLSA provenance
    env_vars_include_patterns:
      - "BUILD_*"
      - "GIT_*"
      - "JENKINS_*"
      - "ENTERPRISE_*"
    env_vars_exclude_patterns:
      - "*PASSWORD*"
      - "*SECRET*"
      - "*TOKEN*"
      - "*KEY*"
  
  # GitHub Actions enterprise integration
  github_actions:
    enabled: true
    workflow_url_template: "https://github.com/enterprise/{{.repo}}/actions/runs/{{.run_id}}"
    capture_context: true
    slsa_integration: true
    attestation_generation: true
  
  # GitLab CI enterprise integration
  gitlab_ci:
    enabled: true
    pipeline_url_template: "https://gitlab.enterprise.com/{{.project}}/-/pipelines/{{.pipeline_id}}"
    capture_merge_request_info: true
    slsa_integration: true

# ORAS (OCI Registry as Storage) configuration
oras:
  enabled: true
  default_registry: "oci-artifacts-prod"
  media_types:
    artifacts: "application/vnd.enterprise.artifact"
    configs: "application/vnd.enterprise.config"
    documentation: "application/vnd.enterprise.docs"
    compliance: "application/vnd.enterprise.compliance"
  annotations:
    created_by: "enterprise-cli"
    slsa_level: "4"
    security_scanned: "true"
  artifact_types:
    - "helm-charts"
    - "terraform-modules"
    - "policy-bundles"
    - "documentation"
    - "compliance-reports"

# Audit and compliance logging
audit:
  enabled: true
  log_level: "info"
  log_file: "/var/log/jfrog-enterprise/audit.log"
  include_payloads: false              # Don't log sensitive data
  retention_days: 2555                 # 7 years for compliance
  structured_logging: true
  log_format: "json"
  correlation_id: true
  slsa_events: true                    # Log SLSA-related events
  compliance_events: true              # Log compliance events

# Performance tuning for enterprise workloads
performance:
  connection_pool_size: 20             # Increased for enterprise
  request_timeout: "60s"               # Longer for enterprise networks
  upload_chunk_size: "128MB"           # Larger chunks for efficiency
  download_chunk_size: "64MB"
  concurrent_uploads: 8                # More concurrency
  concurrent_downloads: 16
  http2_enabled: true                  # HTTP/2 for better performance
  compression_enabled: true            # Enable compression
  keep_alive_timeout: "30s"
  max_idle_connections: 100

# Monitoring and observability
monitoring:
  enabled: true
  metrics_endpoint: "https://metrics.enterprise.com/jfrog-cli"
  report_interval: "1m"                # More frequent for enterprise
  include_system_metrics: true
  include_slsa_metrics: true           # SLSA compliance metrics
  include_security_metrics: true       # Security scan metrics
  prometheus_compatible: true
  custom_tags:
    environment: "production"
    team: "platform"
    compliance_level: "enterprise"

# Enterprise-specific features
enterprise:
  # Multi-tenancy support
  tenancy:
    enabled: true
    default_tenant: "enterprise-main"
    tenant_isolation: "strict"
    cross_tenant_access: false
  
  # Disaster recovery
  disaster_recovery:
    enabled: true
    backup_repositories:
      - "backup-us-east"
      - "backup-eu-west"
    failover_timeout: "30s"
    auto_failover: true
  
  # Compliance reporting
  compliance:
    enabled: true
    report_formats: ["pdf", "json", "xml"]
    report_schedule: "weekly"
    report_recipients:
      - "<EMAIL>"
      - "<EMAIL>"
    include_sbom: true
    include_vulnerability_reports: true
    include_license_reports: true