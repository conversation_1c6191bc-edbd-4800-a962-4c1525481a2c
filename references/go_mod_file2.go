module github.com/yourorg/jfrog-enterprise-cli

go 1.21

require (
	// JFrog official client libraries
	github.com/jfrog/jfrog-client-go v1.34.3
	github.com/jfrog/build-info-go v1.9.18
	github.com/jfrog/gofrog v1.6.8
	
	// CLI framework
	github.com/spf13/cobra v1.8.0
	github.com/spf13/viper v1.18.2
	
	// OCI and container registries
	github.com/google/go-containerregistry v0.19.0
	oras.land/oras-go/v2 v2.4.0
	
	// Porter CNAB integration
	get.porter.sh/porter v1.0.15
	github.com/cnabio/cnab-go v0.16.4
	
	// Package managers
	github.com/Masterminds/semver/v3 v3.2.1
	github.com/cargo-bins/cargo-binstall v1.4.0
	
	// SLSA provenance and security
	github.com/in-toto/in-toto-golang v0.9.0
	github.com/sigstore/cosign/v2 v2.2.1
	github.com/sigstore/rekor v1.3.4
	github.com/sigstore/sigstore v1.8.1
	github.com/slsa-framework/slsa-verifier/v2 v2.4.1
	
	// Dagger CI/CD
	dagger.io/dagger v0.9.3
	
	// Plugin system
	github.com/hashicorp/go-plugin v1.6.0
	github.com/hashicorp/go-hclog v1.6.2
	
	// Progress and UI
	github.com/schollz/progressbar/v3 v3.14.1
	github.com/fatih/color v1.16.0
	
	// Configuration
	gopkg.in/yaml.v3 v3.0.1
	github.com/mitchellh/mapstructure v1.5.0
	
	// Crypto and security
	go.step.sm/crypto v0.36.1
	github.com/google/tink/go v1.7.0
	
	// Utilities
	github.com/pkg/errors v0.9.1
	golang.org/x/sync v0.5.0
	golang.org/x/crypto v0.17.0
)

require (
	// Indirect dependencies for JFrog clients
	github.com/Azure/go-ansiterm v0.0.0-20210617225240-d185dfc1b5a1 // indirect
	github.com/Microsoft/go-winio v0.6.1 // indirect
	github.com/containerd/stargz-snapshotter/estargz v0.14.3 // indirect
	github.com/docker/cli v24.0.0+incompatible // indirect
	github.com/docker/distribution v2.8.2+incompatible // indirect
	github.com/docker/docker v24.0.0+incompatible // indirect
	github.com/docker/docker-credential-helpers v0.7.0 // indirect
	
	// SLSA and security dependencies
	github.com/secure-systems-lab/go-securesystemslib v0.7.0 // indirect
	github.com/transparency-dev/merkle v0.0.2 // indirect
	github.com/cyberphone/json-canonicalization v0.0.0-20231217050601-ba74d44ecf5f // indirect
	
	// Porter and CNAB dependencies
	github.com/cnabio/cnab-to-oci v0.3.5 // indirect
	github.com/docker/go-connections v0.4.0 // indirect
	github.com/docker/go-units v0.5.0 // indirect
	github.com/opencontainers/go-digest v1.0.0 // indirect
	github.com/opencontainers/image-spec v1.1.0-rc5 // indirect
	github.com/opencontainers/runtime-spec v1.0.2 // indirect
	
	// Dagger dependencies
	github.com/Khan/genqlient v0.6.0 // indirect
	github.com/adrg/xdg v0.4.0 // indirect
	github.com/google/uuid v1.4.0 // indirect
	github.com/iancoleman/strcase v0.3.0 // indirect
	github.com/moby/buildkit v0.12.4 // indirect
	github.com/vektah/gqlparser/v2 v2.5.10 // indirect
	
	// Standard library extensions
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/klauspost/compress v1.17.4 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.15 // indirect
	github.com/mitchellh/colorstring v0.0.0-20190213212951-d06e56a500db // indirect
	github.com/moby/term v0.5.0 // indirect
	github.com/pelletier/go-toml/v2 v2.1.1 // indirect
	github.com/rivo/uniseg v0.4.4 // indirect
	github.com/sagikazarmark/locafero v0.4.0 // indirect
	github.com/sagikazarmark/slog-shim v0.1.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/sourcegraph/conc v0.3.0 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/vbatts/tar-split v0.11.5 // indirect
	
	// Go standard library and tooling
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/exp v0.0.0-20231206192017-f3f8817b8deb // indirect
	golang.org/x/mod v0.14.0 // indirect
	golang.org/x/net v0.19.0 // indirect
	golang.org/x/sys v0.15.0 // indirect
	golang.org/x/term v0.15.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	golang.org/x/tools v0.16.1 // indirect
	google.golang.org/protobuf v1.31.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)