# J<PERSON><PERSON> Enhanced CLI

A production-ready, enterprise-grade command-line interface for JFrog Artifactory built on top of official JFrog client libraries. This enhanced CLI provides comprehensive package management, OCI registry support, and advanced enterprise features with a focus on developer experience and operational excellence.

## Features

- **Repository Management**: Create, list, update, and delete repositories
- **Artifact Operations**: Upload, download, and delete artifacts
- **Advanced Search**: AQL (Artifactory Query Language) and name-based search
- **Build Information**: Publish and manage build metadata
- **User Management**: List and manage users and permissions
- **Security Scanning**: Integrate with Xray for vulnerability scanning
- **System Operations**: Health checks, version info, and system status
- **Multiple Authentication**: Support for username/password, API keys, and access tokens
- **Flexible Configuration**: File-based, environment variables, and command-line flags
- **Multiple Output Formats**: Table and JSON output support

## Installation

### Download Binary

Download the latest binary from the [releases page](https://github.com/yourorg/jfrog-cli/releases):

```bash
# Linux
curl -L https://github.com/yourorg/jfrog-cli/releases/latest/download/jfrog-cli-linux-amd64.tar.gz | tar xz
sudo mv jfrog-cli /usr/local/bin/

# macOS
curl -L https://github.com/yourorg/jfrog-cli/releases/latest/download/jfrog-cli-darwin-amd64.tar.gz | tar xz
sudo mv jfrog-cli /usr/local/bin/

# Windows (PowerShell)
Invoke-WebRequest -Uri "https://github.com/yourorg/jfrog-cli/releases/latest/download/jfrog-cli-windows-amd64.zip" -OutFile "jfrog-cli.zip"
Expand-Archive jfrog-cli.zip
```

### Build from Source

```bash
git clone https://github.com/yourorg/jfrog-cli.git
cd jfrog-cli
make build
sudo make install
```

### Docker

```bash
docker pull yourorg/jfrog-cli:latest
docker run --rm -it yourorg/jfrog-cli:latest
```

## Configuration

### Configuration File

Create a configuration file at `~/.jfrog-cli.yaml`:

```yaml
url: "https://your-artifactory.example.com"
username: "your-username"
password: "your-password"
# OR use API key
api_key: "your-api-key"
# OR use access token
token: "your-access-token"
insecure: false
timeout: 30
```

### Environment Variables

```bash
export JFROG_URL="https://your-artifactory.example.com"
export JFROG_USERNAME="your-username"
export JFROG_PASSWORD="your-password"
export JFROG_API_KEY="your-api-key"
export JFROG_TOKEN="your-access-token"
export JFROG_INSECURE="false"
export JFROG_TIMEOUT="30"
```

### Command Line Flags

```bash
jfrog-cli --url "https://your-artifactory.example.com" \
          --username "your-username" \
          --password "your-password" \
          repo list
```

## Usage

### Quick Start

```bash
# Configure the CLI
jfrog-cli config set --url "https://your-artifactory.example.com" \
                     --username "your-username" \
                     --password "your-password"

# Test connection
jfrog-cli system ping

# List repositories
jfrog-cli repo list

# Upload an artifact
jfrog-cli artifact upload ./myfile.jar my-repo/com/example/myfile.jar

# Search for artifacts
jfrog-cli search name "*.jar"
```

### Repository Management

```bash
# List all repositories
jfrog-cli repo list

# List repositories in JSON format
jfrog-cli repo list --format json

# Create a new repository
jfrog-cli repo create my-maven-repo \
  --type local \
  --package-type maven \
  --description "My Maven Repository"

# Delete a repository
jfrog-cli repo delete my-old-repo
```

### Artifact Operations

```bash
# Upload a file
jfrog-cli artifact upload ./target/myapp-1.0.jar \
  maven-local/com/example/myapp/1.0/myapp-1.0.jar

# Download a file
jfrog-cli artifact download \
  maven-local/com/example/myapp/1.0/myapp-1.0.jar \
  ./downloaded-myapp.jar

# Delete an artifact
jfrog-cli artifact delete maven-local/com/example/myapp/1.0/myapp-1.0.jar
```

### Search Operations

```bash
# Search by name pattern
jfrog-cli search name "*.jar"

# Advanced AQL search
jfrog-cli search aql 'items.find({"repo": "maven-local", "name": {"$match": "*.jar"}})'

# Search with JSON output
jfrog-cli search name "myapp*" --format json
```

### Build Information

```bash
# Publish build information
jfrog-cli build publish "my-build" "1.0" ./build-info.json

# List builds for a project
jfrog-cli build list "my-build"

# List builds in JSON format
jfrog-cli build list "my-build" --format json
```

### User Management

```bash
# List all users
jfrog-cli user list

# List users in JSON format
jfrog-cli user list --format json
```

### Security Operations

```bash
# Scan an artifact for vulnerabilities
jfrog-cli security scan maven-local/com/example/myapp/1.0/myapp-1.0.jar
```

### System Operations

```bash
# Ping Artifactory
jfrog-cli system ping

# Get version information
jfrog-cli system version

# Get version in JSON format
jfrog-cli system version --format json
```

## Authentication Methods

### Username and Password

```bash
jfrog-cli config set --url "https://artifactory.example.com" \
                     --username "john.doe" \
                     --password "secret"
```

### API Key

```bash
jfrog-cli config set --url "https://artifactory.example.com" \
                     --api-key "AKCp5bBb3FTf3i8mGk9C2R1v7L8f9X2w6Y4q"
```

### Access Token

```bash
jfrog-cli config set --url "https://artifactory.example.com" \
                     --token "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
```

## Configuration Management

```bash
# Set configuration values
jfrog-cli config set --url "https://new-artifactory.example.com"

# Show current configuration (passwords masked)
jfrog-cli config show

# Use a specific config file
jfrog-cli --config /path/to/config.yaml repo list
```

## Advanced Usage

### Batch Operations

```bash
# Upload multiple files using shell globbing
for file in target/*.jar; do
  jfrog-cli artifact upload "$file" "maven-local/com/example/$(basename "$file")"
done

# Search and download all matching artifacts
jfrog-cli search aql 'items.find({"repo": "maven-local", "name": {"$match": "myapp-*.jar"}})' --format json | \
  jq -r '.results[].downloadUri' | \
  while read url; do
    jfrog-cli artifact download "$url" "./downloads/"
  done
```

### Build Information Example

Create a `build-info.json` file:

```json
{
  "version": "1.6.2",
  "name": "my-build",
  "number": "123",
  "buildAgent": {
    "name": "Jenkins",
    "version": "2.401.3"
  },
  "agent": {
    "name": "jfrog-cli",
    "version": "1.0.0"
  },
  "started": "2024-01-15T10:30:00.000Z",
  "durationMillis": 45000,
  "principal": "john.doe",
  "artifactoryPrincipal": "john.doe",
  "url": "https://jenkins.example.com/job/my-build/123/",
  "modules": [
    {
      "id": "com.example:myapp:1.0",
      "artifacts": [
        {
          "type": "jar",
          "sha1": "da39a3ee5e6b4b0d3255bfef95601890afd80709",
          "md5": "d41d8cd98f00b204e9800998ecf8427e",
          "name": "myapp-1.0.jar",
          "path": "com/example/myapp/1.0/myapp-1.0.jar"
        }
      ]
    }
  ]
}
```

Then publish it:

```bash
jfrog-cli build publish "my-build" "123" ./build-info.json
```

### AQL Query Examples

```bash
# Find all JAR files in a specific repository
jfrog-cli search aql 'items.find({"repo": "maven-local", "name": {"$match": "*.jar"}})'

# Find files modified in the last 7 days
jfrog-cli search aql 'items.find({"modified": {"$last": "7d"}})'

# Find large files (> 100MB)
jfrog-cli search aql 'items.find({"size": {"$gt": "104857600"}})'

# Complex query with multiple conditions
jfrog-cli search aql 'items.find({
  "repo": "maven-local",
  "path": {"$match": "com/example/*"},
  "name": {"$match": "*.jar"},
  "created": {"$gt": "2024-01-01"}
}).include("name", "repo", "path", "size", "created")'
```

## Error Handling

The CLI provides detailed error messages and appropriate exit codes:

- `0`: Success
- `1`: General error
- `2`: Configuration error
- `3`: Authentication error
- `4`: Network error
- `5`: API error

## Security Considerations

- Store credentials securely using your system's credential manager
- Use access tokens instead of passwords when possible
- Regularly rotate API keys and tokens
- Use TLS/SSL for all connections (disable `insecure` flag in production)
- Audit CLI usage through Artifactory's access logs

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Clone the repository
git clone https://github.com/yourorg/jfrog-cli.git
cd jfrog-cli

# Setup development environment
make setup

# Run tests
make test

# Build locally
make build

# Run linter
make lint
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- GitHub Issues: [https://github.com/yourorg/jfrog-cli/issues](https://github.com/yourorg/jfrog-cli/issues)
- Documentation: [https://github.com/yourorg/jfrog-cli/wiki](https://github.com/yourorg/jfrog-cli/wiki)
- JFrog Artifactory Documentation: [https://www.jfrog.com/confluence/display/JFROG/JFrog+Artifactory](https://www.jfrog.com/confluence/display/JFROG/JFrog+Artifactory)

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a list of changes and version history.