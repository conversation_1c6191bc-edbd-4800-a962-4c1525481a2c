#!/bin/bash

# MCStack v9r0 Enhanced Project Structure Setup Script
# This script creates a comprehensive, well-structured project following MCStack principles

set -euo pipefail

PROJECT_NAME="${1:-mcstack-project}"
PROJECT_ROOT="$(pwd)/${PROJECT_NAME}"

echo "🚀 Setting up MCStack v9r0 Enhanced Project: ${PROJECT_NAME}"
echo "📁 Project root: ${PROJECT_ROOT}"

# Create main project directory
mkdir -p "${PROJECT_ROOT}"
cd "${PROJECT_ROOT}"

# Create .ai directory for MCStack configuration
mkdir -p .ai/{governance,templates}

# Create GitHub Actions workflows
mkdir -p .github/workflows

# Create .well-known directory for metadata
mkdir -p .well-known/{slsa-provenance,transparency-logs,compliance}

# Create API definitions directory (Proto-first)
mkdir -p api/{protobuf/{v1,common},openapi,graphql}

# Create build configurations
mkdir -p build/{nix,slsa}

# Create CLI and entry points
mkdir -p cmd/{server,cli,tools}

# Create configuration files
mkdir -p configs

# Create deployment configurations
mkdir -p deployments/{helm,kustomize,terraform}

# Create comprehensive documentation
mkdir -p docs/{architecture,api,compliance,governance,safety,user}

# Create internal packages
mkdir -p internal/{attestation,crypto,governance,telemetry,security}

# Create Dagger modules
mkdir -p modules/{build,security,testing,publishing,verification,governance,quantum,resilience,xai,telemetry}

# Create public packages
mkdir -p pkg/{client,types,utils}

# Create operational runbooks
mkdir -p playbooks/{incident-response,chaos-engineering,deployment,governance}

# Create policy directories
mkdir -p policies/{security,compliance,governance}

# Create automation scripts
mkdir -p scripts/{setup,build,deploy}

# Create simulation directories
mkdir -p simulations/{chaos,safety,performance}

# Create source code directories
mkdir -p src/{main,test,resources}

# Create comprehensive testing
mkdir -p tests/{e2e,integration,unit,chaos,security,compliance}

# Create design system tokens
mkdir -p tokens

# Create configuration templates
mkdir -p .tmpl/{docker,k8s,ci}

# Create reference directory
mkdir -p reference/{original,legacy,examples}

echo "✅ Directory structure created successfully!"

# Create essential configuration files
echo "📝 Creating essential configuration files..."

# Create dagger.json
cat > dagger.json << 'EOF'
{
  "name": "mcstack-project",
  "sdk": "go",
  "source": ".",
  "dependencies": [],
  "exclude": [
    "reference/",
    "docs/",
    "tests/",
    ".git/"
  ]
}
EOF

# Create go.mod
cat > go.mod << 'EOF'
module github.com/yourorg/mcstack-project

go 1.21

require (
    dagger.io/dagger v0.9.0
)
EOF

# Create Makefile
cat > Makefile << 'EOF'
# MCStack v9r0 Enhanced Makefile

.PHONY: help build test lint security deploy clean

# Default target
help: ## Show this help message
	@echo "MCStack v9r0 Enhanced Project"
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## Build the project
	@echo "🔨 Building project..."
	dagger call build

test: ## Run all tests
	@echo "🧪 Running tests..."
	dagger call test

lint: ## Run linting
	@echo "🔍 Running linters..."
	dagger call lint

security: ## Run security scans
	@echo "🛡️ Running security scans..."
	dagger call security-scan

deploy: ## Deploy the project
	@echo "🚀 Deploying project..."
	dagger call deploy

clean: ## Clean build artifacts
	@echo "🧹 Cleaning up..."
	rm -rf build/
	docker system prune -f

.DEFAULT_GOAL := help
EOF

# Create README.rini (Intelligent README)
cat > README.rini << 'EOF'
# MCStack v9r0 Enhanced Project

A production-ready, enterprise-grade project built following MCStack v9r0 Enhanced principles with SLSA Level 5 compliance, governance autonomy levels, and anti-fragile design patterns.

## 🌟 Features

- **SLSA Level 5 Compliance**: Highest level supply chain security with hermetic builds
- **Governance Autonomy Levels**: Structured decision-making with automated governance
- **Anti-Fragile Design**: Self-healing capabilities and chaos engineering
- **Proto-First APIs**: Schema-driven development with strong contracts
- **Comprehensive Testing**: Unit, integration, E2E, chaos, and security testing
- **Observability**: Full telemetry, logging, and monitoring integration

## 🚀 Quick Start

```bash
# Build the project
make build

# Run tests
make test

# Deploy
make deploy
```

## 📚 Documentation

- [Architecture](docs/architecture/) - Arc42 architecture documentation
- [API Documentation](docs/api/) - Generated API documentation
- [User Guide](docs/user/) - User guides and tutorials
- [Governance](docs/governance/) - GAL definitions and procedures

## 🛡️ Security

This project follows SLSA Level 5 compliance requirements:
- Hermetic builds with complete isolation
- Comprehensive provenance generation
- Signed attestations for all artifacts
- Vulnerability scanning at all stages

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the Apache 2.0 License - see the [LICENSE](LICENSE) file for details.

---

*Built with MCStack v9r0 Enhanced - Verifiably Safe, Governable, Secure, Explainable, and Anti-Fragile*
EOF

echo "✅ Essential configuration files created!"

echo ""
echo "🎉 MCStack v9r0 Enhanced project structure created successfully!"
echo ""
echo "📁 Project location: ${PROJECT_ROOT}"
echo ""
echo "🔧 Next steps:"
echo "1. cd ${PROJECT_NAME}"
echo "2. Initialize git repository: git init"
echo "3. Add remote origin: git remote add origin <your-repo-url>"
echo "4. Start development: make build"
echo ""
echo "📖 For detailed documentation, see: ${PROJECT_ROOT}/docs/"
echo ""
