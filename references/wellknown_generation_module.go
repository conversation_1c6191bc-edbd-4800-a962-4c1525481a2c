// Package wellknown provides comprehensive .well-known directory auto-generation
// This module generates standardized security, compliance, and discovery files
// according to RFC 5785 and modern security standards for SLSA Level 5 compliance
//
// MCStack v9r0 Enhanced .well-known Features:
// - Comprehensive security.txt with contact and policy information
// - SLSA provenance and attestation metadata
// - OpenID Connect discovery and JW<PERSON> endpoints
// - Digital asset links for mobile app verification
// - Certificate transparency logs and ACME challenge support
// - Post-quantum cryptography algorithm announcements
// - Supply chain transparency and SBOM metadata
// - Governance and compliance framework declarations
package wellknown

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"dagger.io/dagger"
)

// WellKnownModule provides comprehensive .well-known directory generation
type WellKnownModule struct {
	config    *WellKnownConfig
	metadata  *OrganizationMetadata
	security  *SecurityConfiguration
	compliance *ComplianceConfiguration
	discovery *DiscoveryConfiguration
}

// WellKnownConfig defines .well-known generation configuration
type WellKnownConfig struct {
	// Core Files
	SecurityTxtEnabled       bool `json:"securityTxtEnabled"`
	ChangePasswordEnabled    bool `json:"changePasswordEnabled"`
	PasskeyConfigEnabled     bool `json:"passkeyConfigEnabled"`
	
	// SLSA and Supply Chain
	SLSAProvenanceEnabled    bool `json:"slsaProvenanceEnabled"`
	SBOMMetadataEnabled      bool `json:"sbomMetadataEnabled"`
	AttestationEnabled       bool `json:"attestationEnabled"`
	TransparencyLogEnabled   bool `json:"transparencyLogEnabled"`
	
	// OpenID Connect and OAuth
	OpenIDConfigEnabled      bool `json:"openidConfigEnabled"`
	JWKSEnabled              bool `json:"jwksEnabled"`
	OAuth2MetadataEnabled    bool `json:"oauth2MetadataEnabled"`
	
	// Mobile and Web
	AssetLinksEnabled        bool `json:"assetLinksEnabled"`
	AppleAppAssociationEnabled bool `json:"appleAppAssociationEnabled"`
	WebAuthnEnabled          bool `json:"webauthnEnabled"`
	
	// Certificate and ACME
	ACMEChallengeEnabled     bool `json:"acmeChallengeEnabled"`
	CAIssuersEnabled         bool `json:"caIssuersEnabled"`
	CertTransparencyEnabled  bool `json:"certTransparencyEnabled"`
	
	// Post-Quantum Cryptography
	PQCAnnouncementEnabled   bool `json:"pqcAnnouncementEnabled"`
	QuantumSafeEnabled       bool `json:"quantumSafeEnabled"`
	
	// Compliance and Governance
	ComplianceFrameworkEnabled bool `json:"complianceFrameworkEnabled"`
	OSCALMetadataEnabled     bool `json:"oscalMetadataEnabled"`
	GovernanceEnabled        bool `json:"governanceEnabled"`
	
	// Contact and Support
	SupportEnabled           bool `json:"supportEnabled"`
	ContactEnabled           bool `json:"contactEnabled"`
	HumansEnabled            bool `json:"humansEnabled"`
	
	// API and Service Discovery
	APIDiscoveryEnabled      bool `json:"apiDiscoveryEnabled"`
	ServiceDiscoveryEnabled  bool `json:"serviceDiscoveryEnabled"`
	HealthCheckEnabled       bool `json:"healthCheckEnabled"`
}

// OrganizationMetadata contains organization information
type OrganizationMetadata struct {
	Name               string    `json:"name"`
	Domain             string    `json:"domain"`
	ContactEmail       string    `json:"contactEmail"`
	SecurityEmail      string    `json:"securityEmail"`
	SupportEmail       string    `json:"supportEmail"`
	Website            string    `json:"website"`
	Description        string    `json:"description"`
	Founded            time.Time `json:"founded"`
	Headquarters       string    `json:"headquarters"`
	Industry           string    `json:"industry"`
	ComplianceFrameworks []string `json:"complianceFrameworks"`
	CertificationsBadges []Certification `json:"certificationsBadges"`
}

// SecurityConfiguration defines security-related metadata
type SecurityConfiguration struct {
	// Contact Information
	SecurityContacts       []SecurityContact `json:"securityContacts"`
	IncidentResponseTeam   string           `json:"incidentResponseTeam"`
	
	// Policy Information
	SecurityPolicyURL      string           `json:"securityPolicyUrl"`
	VulnerabilityReporting string           `json:"vulnerabilityReporting"`
	BugBountyProgram       string           `json:"bugBountyProgram"`
	ResponsibleDisclosure  string           `json:"responsibleDisclosure"`
	
	// Encryption and Cryptography
	PreferredLanguages     []string         `json:"preferredLanguages"`
	PGPKeys                []PGPKey         `json:"pgpKeys"`
	PostQuantumAlgorithms  []PQCAlgorithm   `json:"postQuantumAlgorithms"`
	
	// Certificate Information
	CertificateAuthorities []CAInfo         `json:"certificateAuthorities"`
	CertificatePinning     []PinInfo        `json:"certificatePinning"`
	
	// Security Standards
	SecurityStandards      []SecurityStandard `json:"securityStandards"`
	ComplianceLevel        string            `json:"complianceLevel"`
	AuditInformation       []AuditInfo       `json:"auditInformation"`
}

// ComplianceConfiguration defines compliance and governance metadata
type ComplianceConfiguration struct {
	// Regulatory Compliance
	Frameworks            []ComplianceFramework `json:"frameworks"`
	Certifications        []Certification       `json:"certifications"`
	AuditReports          []AuditReport         `json:"auditReports"`
	
	// SLSA Information
	SLSALevel             int                   `json:"slsaLevel"`
	SLSARequirements      []string              `json:"slsaRequirements"`
	BuilderInformation    BuilderInfo           `json:"builderInformation"`
	
	// Supply Chain
	SupplierDeclarations  []SupplierDeclaration `json:"supplierDeclarations"`
	SBOMLocations         []SBOMLocation        `json:"sbomLocations"`
	AttestationEndpoints  []AttestationEndpoint `json:"attestationEndpoints"`
	
	// Governance
	GovernanceFramework   string                `json:"governanceFramework"`
	PolicyManagement      PolicyManagement      `json:"policyManagement"`
	RiskManagement        RiskManagement        `json:"riskManagement"`
}

// DiscoveryConfiguration defines service discovery metadata
type DiscoveryConfiguration struct {
	// API Discovery
	APIEndpoints          []APIEndpoint         `json:"apiEndpoints"`
	OpenAPISpecifications []OpenAPISpec         `json:"openApiSpecifications"`
	GraphQLEndpoints      []GraphQLEndpoint     `json:"graphqlEndpoints"`
	
	// Authentication and Authorization
	AuthenticationMethods []AuthMethod          `json:"authenticationMethods"`
	IdentityProviders     []IdentityProvider    `json:"identityProviders"`
	JWKSEndpoint          string               `json:"jwksEndpoint"`
	
	// Health and Status
	HealthEndpoints       []HealthEndpoint      `json:"healthEndpoints"`
	StatusPages           []StatusPage          `json:"statusPages"`
	MetricsEndpoints      []MetricsEndpoint     `json:"metricsEndpoints"`
	
	// Mobile and Web App
	MobileAppLinks        []MobileAppLink       `json:"mobileAppLinks"`
	WebAppManifest        string               `json:"webAppManifest"`
	ServiceWorker         string               `json:"serviceWorker"`
}

// Supporting types
type SecurityContact struct {
	Name     string `json:"name"`
	Email    string `json:"email"`
	Phone    string `json:"phone,omitempty"`
	Role     string `json:"role"`
	PGPKey   string `json:"pgpKey,omitempty"`
	Languages []string `json:"languages,omitempty"`
}

type PGPKey struct {
	KeyID       string `json:"keyId"`
	Fingerprint string `json:"fingerprint"`
	PublicKey   string `json:"publicKey"`
	Purpose     string `json:"purpose"`
}

type PQCAlgorithm struct {
	Name        string `json:"name"`
	Type        string `json:"type"` // KEM, Signature, etc.
	Standard    string `json:"standard"` // NIST, etc.
	Implemented bool   `json:"implemented"`
	Planned     bool   `json:"planned"`
}

type CAInfo struct {
	Name         string   `json:"name"`
	URL          string   `json:"url"`
	Fingerprints []string `json:"fingerprints"`
	TrustLevel   string   `json:"trustLevel"`
}

type PinInfo struct {
	Hostname     string   `json:"hostname"`
	Pins         []string `json:"pins"`
	Algorithm    string   `json:"algorithm"`
	MaxAge       int      `json:"maxAge"`
}

type SecurityStandard struct {
	Name         string `json:"name"`
	Version      string `json:"version"`
	Compliance   string `json:"compliance"`
	CertifiedBy  string `json:"certifiedBy,omitempty"`
	ValidUntil   string `json:"validUntil,omitempty"`
}

type AuditInfo struct {
	Type         string `json:"type"`
	Auditor      string `json:"auditor"`
	Date         string `json:"date"`
	ReportURL    string `json:"reportUrl,omitempty"`
	Scope        string `json:"scope"`
}

type Certification struct {
	Name         string `json:"name"`
	Issuer       string `json:"issuer"`
	IssuedDate   string `json:"issuedDate"`
	ExpiryDate   string `json:"expiryDate,omitempty"`
	CertificateURL string `json:"certificateUrl,omitempty"`
	BadgeURL     string `json:"badgeUrl,omitempty"`
}

type ComplianceFramework struct {
	Name         string   `json:"name"`
	Version      string   `json:"version"`
	Controls     []string `json:"controls"`
	Status       string   `json:"status"`
	LastAssessed string   `json:"lastAssessed"`
}

type AuditReport struct {
	Type         string `json:"type"`
	Period       string `json:"period"`
	ReportURL    string `json:"reportUrl"`
	Summary      string `json:"summary"`
}

type BuilderInfo struct {
	ID           string            `json:"id"`
	Version      string            `json:"version"`
	Capabilities []string          `json:"capabilities"`
	Attestations []string          `json:"attestations"`
	Metadata     map[string]string `json:"metadata"`
}

type SupplierDeclaration struct {
	Supplier     string   `json:"supplier"`
	Components   []string `json:"components"`
	SLSALevel    int      `json:"slsaLevel"`
	AttestationURL string `json:"attestationUrl"`
}

type SBOMLocation struct {
	Format    string `json:"format"`
	URL       string `json:"url"`
	Algorithm string `json:"algorithm"`
	Hash      string `json:"hash"`
}

type AttestationEndpoint struct {
	Type      string `json:"type"`
	URL       string `json:"url"`
	Format    string `json:"format"`
	PublicKey string `json:"publicKey"`
}

type PolicyManagement struct {
	PolicyFramework string   `json:"policyFramework"`
	PolicyEngine    string   `json:"policyEngine"`
	Policies        []Policy `json:"policies"`
}

type RiskManagement struct {
	Framework    string     `json:"framework"`
	RiskRegister string     `json:"riskRegister"`
	Assessments  []RiskItem `json:"assessments"`
}

type Policy struct {
	Name        string `json:"name"`
	Version     string `json:"version"`
	URL         string `json:"url"`
	Scope       string `json:"scope"`
	LastUpdated string `json:"lastUpdated"`
}

type RiskItem struct {
	ID          string `json:"id"`
	Category    string `json:"category"`
	Severity    string `json:"severity"`
	Mitigation  string `json:"mitigation"`
	Owner       string `json:"owner"`
}

type APIEndpoint struct {
	Path        string `json:"path"`
	Method      string `json:"method"`
	Description string `json:"description"`
	Version     string `json:"version"`
	OpenAPISpec string `json:"openApiSpec,omitempty"`
}

type OpenAPISpec struct {
	Version     string `json:"version"`
	URL         string `json:"url"`
	Description string `json:"description"`
}

type GraphQLEndpoint struct {
	URL         string `json:"url"`
	Schema      string `json:"schema"`
	Playground  string `json:"playground,omitempty"`
}

type AuthMethod struct {
	Type        string   `json:"type"`
	Description string   `json:"description"`
	Endpoints   []string `json:"endpoints"`
	Scopes      []string `json:"scopes,omitempty"`
}

type IdentityProvider struct {
	Name      string `json:"name"`
	Type      string `json:"type"`
	Issuer    string `json:"issuer"`
	Discovery string `json:"discovery"`
}

type HealthEndpoint struct {
	Path        string `json:"path"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

type StatusPage struct {
	URL         string `json:"url"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

type MetricsEndpoint struct {
	Path        string `json:"path"`
	Format      string `json:"format"`
	Description string `json:"description"`
}

type MobileAppLink struct {
	Platform    string   `json:"platform"`
	PackageID   string   `json:"packageId"`
	Fingerprint []string `json:"fingerprint"`
}

// NewWellKnownModule creates a new .well-known generation module
func NewWellKnownModule() *WellKnownModule {
	config := &WellKnownConfig{
		SecurityTxtEnabled:         true,
		ChangePasswordEnabled:      true,
		PasskeyConfigEnabled:       true,
		SLSAProvenanceEnabled:      true,
		SBOMMetadataEnabled:        true,
		AttestationEnabled:         true,
		TransparencyLogEnabled:     true,
		OpenIDConfigEnabled:        false, // Enable for OIDC providers
		JWKSEnabled:               false, // Enable for OIDC providers
		OAuth2MetadataEnabled:      false, // Enable for OAuth2 providers
		AssetLinksEnabled:          false, // Enable for mobile apps
		AppleAppAssociationEnabled: false, // Enable for iOS apps
		WebAuthnEnabled:           true,
		ACMEChallengeEnabled:      true,
		CAIssuersEnabled:          true,
		CertTransparencyEnabled:   true,
		PQCAnnouncementEnabled:    true,
		QuantumSafeEnabled:        true,
		ComplianceFrameworkEnabled: true,
		OSCALMetadataEnabled:      true,
		GovernanceEnabled:         true,
		SupportEnabled:            true,
		ContactEnabled:            true,
		HumansEnabled:             true,
		APIDiscoveryEnabled:       true,
		ServiceDiscoveryEnabled:   true,
		HealthCheckEnabled:        true,
	}

	return &WellKnownModule{
		config: config,
		metadata: &OrganizationMetadata{
			Name:         "MCStack",
			Domain:       "mcstack.ai",
			ContactEmail: "<EMAIL>",
			SecurityEmail: "<EMAIL>",
			SupportEmail: "<EMAIL>",
			Website:      "https://mcstack.ai",
			Description:  "World-class SLSA Level 5 Dagger modules for secure, verifiable software supply chains",
			Industry:     "Software Security & DevOps",
			ComplianceFrameworks: []string{"SLSA Level 5", "NIST Cybersecurity Framework", "SOC 2", "ISO 27001"},
		},
		security: &SecurityConfiguration{
			SecurityContacts: []SecurityContact{
				{
					Name:      "MCStack Security Team",
					Email:     "<EMAIL>",
					Role:      "Security Response Team",
					Languages: []string{"en", "fr"},
				},
			},
			SecurityPolicyURL:      "https://mcstack.ai/security/policy",
			VulnerabilityReporting: "https://mcstack.ai/security/reporting",
			BugBountyProgram:      "https://mcstack.ai/security/bounty",
			ResponsibleDisclosure: "https://mcstack.ai/security/disclosure",
			PreferredLanguages:    []string{"en"},
			PostQuantumAlgorithms: []PQCAlgorithm{
				{Name: "Kyber", Type: "KEM", Standard: "NIST", Implemented: true},
				{Name: "Dilithium", Type: "Signature", Standard: "NIST", Implemented: true},
				{Name: "Falcon", Type: "Signature", Standard: "NIST", Implemented: true},
				{Name: "SPHINCS+", Type: "Signature", Standard: "NIST", Planned: true},
			},
			SecurityStandards: []SecurityStandard{
				{Name: "SLSA", Version: "v1.0", Compliance: "Level 5"},
				{Name: "NIST Cybersecurity Framework", Version: "v1.1", Compliance: "Full"},
				{Name: "OWASP Top 10", Version: "2021", Compliance: "Compliant"},
			},
			ComplianceLevel: "SLSA Level 5",
		},
		compliance: &ComplianceConfiguration{
			SLSALevel: 5,
			SLSARequirements: []string{
				"Hermetic builds",
				"Complete provenance",
				"Two-party review",
				"Immutable references",
				"Non-falsifiable provenance",
			},
			BuilderInformation: BuilderInfo{
				ID:      "https://mcstack.ai/dagger-slsa5-builder@v1.0.0",
				Version: "v9r0_enhanced",
				Capabilities: []string{
					"hermetic-builds",
					"slsa-provenance",
					"post-quantum-crypto",
					"vulnerability-scanning",
					"attestation-signing",
				},
				Attestations: []string{
					"https://slsa.dev/provenance/v1",
					"https://in-toto.io/Statement/v0.1",
					"https://mcstack.ai/attestations/security-scan/v1",
				},
			},
			GovernanceFramework: "MCStack v9r0 Enhanced",
		},
		discovery: &DiscoveryConfiguration{
			APIEndpoints: []APIEndpoint{
				{Path: "/api/v1/health", Method: "GET", Description: "Health check endpoint", Version: "v1"},
				{Path: "/api/v1/builds", Method: "POST", Description: "Create SLSA5 build", Version: "v1"},
			},
			HealthEndpoints: []HealthEndpoint{
				{Path: "/health", Type: "liveness", Description: "Service liveness check"},
				{Path: "/ready", Type: "readiness", Description: "Service readiness check"},
			},
		},
	}
}

// GenerateWellKnownFiles creates all .well-known files
func (wkm *WellKnownModule) GenerateWellKnownFiles(ctx context.Context, dag *dagger.Client) (*dagger.Container, error) {
	// Create base container
	container := dag.Container().
		From("alpine:latest").
		WithExec([]string{"apk", "add", "--no-cache", "openssl", "curl", "jq"})

	// Create .well-known directory structure
	container = container.
		WithExec([]string{"mkdir", "-p", ".well-known"})

	// Generate security.txt
	if wkm.config.SecurityTxtEnabled {
		container = wkm.generateSecurityTxt(container)
	}

	// Generate SLSA provenance metadata
	if wkm.config.SLSAProvenanceEnabled {
		container = wkm.generateSLSAMetadata(container)
	}

	// Generate SBOM metadata
	if wkm.config.SBOMMetadataEnabled {
		container = wkm.generateSBOMMetadata(container)
	}

	// Generate OpenID Connect configuration
	if wkm.config.OpenIDConfigEnabled {
		container = wkm.generateOpenIDConfig(container)
	}

	// Generate JWKS
	if wkm.config.JWKSEnabled {
		container = wkm.generateJWKS(container)
	}

	// Generate WebAuthn configuration
	if wkm.config.WebAuthnEnabled {
		container = wkm.generateWebAuthnConfig(container)
	}

	// Generate post-quantum cryptography announcement
	if wkm.config.PQCAnnouncementEnabled {
		container = wkm.generatePQCAnnouncement(container)
	}

	// Generate compliance framework metadata
	if wkm.config.ComplianceFrameworkEnabled {
		container = wkm.generateComplianceMetadata(container)
	}

	// Generate API discovery
	if wkm.config.APIDiscoveryEnabled {
		container = wkm.generateAPIDiscovery(container)
	}

	// Generate support and contact information
	if wkm.config.SupportEnabled {
		container = wkm.generateSupportInfo(container)
	}

	// Generate humans.txt
	if wkm.config.HumansEnabled {
		container = wkm.generateHumansTxt(container)
	}

	// Generate health check configuration
	if wkm.config.HealthCheckEnabled {
		container = wkm.generateHealthConfig(container)
	}

	return container, nil
}

// generateSecurityTxt creates security.txt file according to RFC 9116
func (wkm *WellKnownModule) generateSecurityTxt(container *dagger.Container) *dagger.Container {
	securityTxt := fmt.Sprintf(`# Security Policy for %s
# Generated by MCStack v9r0 Enhanced
# Compliant with RFC 9116

Contact: mailto:%s
Contact: %s/security/contact
Expires: %s
Encryption: %s/security/pgp-key.asc
Acknowledgments: %s/security/acknowledgments
Policy: %s/security/policy
Hiring: %s/careers/security

Preferred-Languages: %s

# Bug Bounty Program
# We offer rewards for security vulnerabilities
# Canonical: %s/.well-known/security.txt

# SLSA Level 5 Compliance
# This organization maintains SLSA Level 5 compliance
# Provenance: %s/.well-known/slsa-provenance.json
# Build attestations available at: %s/.well-known/attestations/

# Post-Quantum Cryptography
# We support post-quantum cryptographic algorithms
# Details: %s/.well-known/pqc-announcement.json

# Supply Chain Security
# SBOM available at: %s/.well-known/sbom.json
# Transparency logs: %s/.well-known/transparency-logs.json

# Emergency Contact (24/7)
# For critical security incidents: %s
`,
		wkm.metadata.Name,
		wkm.metadata.SecurityEmail,
		wkm.metadata.Website,
		time.Now().AddDate(1, 0, 0).Format("2006-01-02T15:04:05Z"),
		wkm.metadata.Website,
		wkm.metadata.Website,
		wkm.metadata.Website,
		wkm.metadata.Website,
		strings.Join(wkm.security.PreferredLanguages, ", "),
		wkm.metadata.Website,
		wkm.metadata.Website,
		wkm.metadata.Website,
		wkm.metadata.Website,
		wkm.metadata.Website,
		wkm.metadata.Website,
		wkm.metadata.SecurityEmail,
	)

	return container.WithNewFile(".well-known/security.txt", securityTxt)
}

// generateSLSAMetadata creates SLSA provenance metadata
func (wkm *WellKnownModule) generateSLSAMetadata(container *dagger.Container) *dagger.Container {
	slsaMetadata := map[string]interface{}{
		"slsa_version": "v1.0",
		"slsa_level":   wkm.compliance.SLSALevel,
		"builder": map[string]interface{}{
			"id":           wkm.compliance.BuilderInformation.ID,
			"version":      wkm.compliance.BuilderInformation.Version,
			"capabilities": wkm.compliance.BuilderInformation.Capabilities,
		},
		"requirements_met": wkm.compliance.SLSARequirements,
		"attestation_formats": []string{
			"application/vnd.in-toto+json",
			"application/vnd.slsa.provenance+json",
		},
		"provenance_endpoints": []string{
			fmt.Sprintf("%s/.well-known/provenance/", wkm.metadata.Website),
			fmt.Sprintf("%s/api/v1/provenance", wkm.metadata.Website),
		},
		"transparency_logs": []string{
			"https://rekor.sigstore.dev",
			fmt.Sprintf("%s/.well-known/transparency-logs.json", wkm.metadata.Website),
		},
		"verification_keys": map[string]string{
			"primary": fmt.Sprintf("%s/.well-known/verification-key.pem", wkm.metadata.Website),
			"backup":  fmt.Sprintf("%s/.well-known/verification-key-backup.pem", wkm.metadata.Website),
		},
		"metadata": map[string]interface{}{
			"generated_at":    time.Now().Format(time.RFC3339),
			"mcstack_version": "v9r0_enhanced",
			"generator":       "MCStack .well-known auto-generation",
		},
	}

	slsaJSON, _ := json.MarshalIndent(slsaMetadata, "", "  ")
	return container.WithNewFile(".well-known/slsa-provenance.json", string(slsaJSON))
}

// generateSBOMMetadata creates SBOM metadata and locations
func (wkm *WellKnownModule) generateSBOMMetadata(container *dagger.Container) *dagger.Container {
	sbomMetadata := map[string]interface{}{
		"sbom_formats": []string{"SPDX", "CycloneDX", "SWID"},
		"locations": []map[string]string{
			{
				"format": "application/spdx+json",
				"url":    fmt.Sprintf("%s/.well-known/sbom.spdx.json", wkm.metadata.Website),
			},
			{
				"format": "application/vnd.cyclonedx+json",
				"url":    fmt.Sprintf("%s/.well-known/sbom.cyclonedx.json", wkm.metadata.Website),
			},
		},
		"generation": map[string]interface{}{
			"automated":    true,
			"frequency":    "every_build",
			"tools":        []string{"cyclonedx-gomod", "spdx-sbom-generator", "syft"},
			"last_updated": time.Now().Format(time.RFC3339),
		},
		"verification": map[string]interface{}{
			"signatures": map[string]string{
				"cosign": fmt.Sprintf("%s/.well-known/sbom.sig", wkm.metadata.Website),
				"pgp":    fmt.Sprintf("%s/.well-known/sbom.asc", wkm.metadata.Website),
			},
			"attestations": fmt.Sprintf("%s/.well-known/sbom-attestation.json", wkm.metadata.Website),
		},
		"metadata": map[string]interface{}{
			"supplier":      wkm.metadata.Name,
			"contact":       wkm.metadata.ContactEmail,
			"license":       "Apache-2.0",
			"slsa_level":    wkm.compliance.SLSALevel,
			"scan_results":  fmt.Sprintf("%s/.well-known/security-scan.json", wkm.metadata.Website),
		},
	}

	sbomJSON, _ := json.MarshalIndent(sbomMetadata, "", "  ")
	return container.WithNewFile(".well-known/sbom.json", string(sbomJSON))
}

// generateOpenIDConfig creates OpenID Connect configuration
func (wkm *WellKnownModule) generateOpenIDConfig(container *dagger.Container) *dagger.Container {
	openidConfig := map[string]interface{}{
		"issuer":                                 fmt.Sprintf("https://%s", wkm.metadata.Domain),
		"authorization_endpoint":                 fmt.Sprintf("https://%s/auth/authorize", wkm.metadata.Domain),
		"token_endpoint":                        fmt.Sprintf("https://%s/auth/token", wkm.metadata.Domain),
		"userinfo_endpoint":                     fmt.Sprintf("https://%s/auth/userinfo", wkm.metadata.Domain),
		"jwks_uri":                              fmt.Sprintf("https://%s/.well-known/jwks.json", wkm.metadata.Domain),
		"response_types_supported":              []string{"code", "id_token", "token id_token"},
		"subject_types_supported":               []string{"public"},
		"id_token_signing_alg_values_supported": []string{"RS256", "ES256", "PS256", "EdDSA"},
		"scopes_supported":                      []string{"openid", "profile", "email"},
		"claims_supported":                      []string{"sub", "iss", "aud", "exp", "iat", "name", "email"},
		"code_challenge_methods_supported":      []string{"S256"},
		"grant_types_supported":                 []string{"authorization_code", "client_credentials"},
	}

	openidJSON, _ := json.MarshalIndent(openidConfig, "", "  ")
	return container.WithNewFile(".well-known/openid_configuration", string(openidJSON))
}

// generateJWKS creates JSON Web Key Set
func (wkm *WellKnownModule) generateJWKS(container *dagger.Container) *dagger.Container {
	jwks := map[string]interface{}{
		"keys": []map[string]interface{}{
			{
				"kty": "RSA",
				"use": "sig",
				"kid": "mcstack-2024-01",
				"alg": "RS256",
				"n":   "example_modulus_would_go_here",
				"e":   "AQAB",
			},
			{
				"kty": "EC",
				"use": "sig",
				"kid": "mcstack-2024-02",
				"alg": "ES256",
				"crv": "P-256",
				"x":   "example_x_coordinate",
				"y":   "example_y_coordinate",
			},
		},
	}

	jwksJSON, _ := json.MarshalIndent(jwks, "", "  ")
	return container.WithNewFile(".well-known/jwks.json", string(jwksJSON))
}

// generateWebAuthnConfig creates WebAuthn configuration
func (wkm *WellKnownModule) generateWebAuthnConfig(container *dagger.Container) *dagger.Container {
	webauthnConfig := map[string]interface{}{
		"origin":  fmt.Sprintf("https://%s", wkm.metadata.Domain),
		"rpId":    wkm.metadata.Domain,
		"rpName":  wkm.metadata.Name,
		"timeout": 60000,
		"attestation": map[string]interface{}{
			"conveyancePreference": "direct",
			"formats":              []string{"packed", "tpm", "android-key", "android-safetynet", "fido-u2f"},
		},
		"authenticatorSelection": map[string]interface{}{
			"authenticatorAttachment": "cross-platform",
			"userVerification":        "preferred",
			"residentKey":             "preferred",
		},
		"extensions": map[string]bool{
			"credProps": true,
			"hmacSecret": true,
		},
		"algorithms": []map[string]interface{}{
			{"type": "public-key", "alg": -7},  // ES256
			{"type": "public-key", "alg": -257}, // RS256
			{"type": "public-key", "alg": -8},   // EdDSA
		},
	}

	webauthnJSON, _ := json.MarshalIndent(webauthnConfig, "", "  ")
	return container.WithNewFile(".well-known/webauthn.json", string(webauthnJSON))
}

// generatePQCAnnouncement creates post-quantum cryptography announcement
func (wkm *WellKnownModule) generatePQCAnnouncement(container *dagger.Container) *dagger.Container {
	pqcAnnouncement := map[string]interface{}{
		"quantum_safe_status": "implementing",
		"nist_standards_compliance": map[string]interface{}{
			"kyber":     map[string]interface{}{"status": "implemented", "version": "Kyber-768"},
			"dilithium": map[string]interface{}{"status": "implemented", "version": "Dilithium3"},
			"falcon":    map[string]interface{}{"status": "implemented", "version": "Falcon-512"},
			"sphincs":   map[string]interface{}{"status": "planned", "version": "SPHINCS+-128s"},
		},
		"hybrid_mode": map[string]interface{}{
			"enabled":     true,
			"description": "Classical and post-quantum algorithms used together",
			"algorithms":  []string{"RSA+Kyber", "ECDSA+Dilithium"},
		},
		"migration_timeline": map[string]interface{}{
			"phase1": map[string]string{
				"start":       "2024-01-01",
				"description": "Hybrid implementation",
				"status":      "completed",
			},
			"phase2": map[string]string{
				"start":       "2024-06-01",
				"description": "Full post-quantum transition",
				"status":      "in_progress",
			},
			"phase3": map[string]string{
				"start":       "2025-01-01",
				"description": "Legacy algorithm deprecation",
				"status":      "planned",
			},
		},
		"endpoints": map[string]interface{}{
			"quantum_safe_api": fmt.Sprintf("https://%s/api/v1/pqc", wkm.metadata.Domain),
			"key_exchange":     fmt.Sprintf("https://%s/.well-known/pqc-keys.json", wkm.metadata.Domain),
			"algorithms_list":  fmt.Sprintf("https://%s/.well-known/pqc-algorithms.json", wkm.metadata.Domain),
		},
		"contact": map[string]string{
			"email":       wkm.metadata.SecurityEmail,
			"team":        "Cryptography Engineering Team",
			"research":    fmt.Sprintf("%s/research/pqc", wkm.metadata.Website),
		},
		"metadata": map[string]interface{}{
			"last_updated":    time.Now().Format(time.RFC3339),
			"next_review":     time.Now().AddDate(0, 3, 0).Format(time.RFC3339),
			"mcstack_version": "v9r0_enhanced",
		},
	}

	pqcJSON, _ := json.MarshalIndent(pqcAnnouncement, "", "  ")
	return container.WithNewFile(".well-known/pqc-announcement.json", string(pqcJSON))
}

// generateComplianceMetadata creates compliance framework metadata
func (wkm *WellKnownModule) generateComplianceMetadata(container *dagger.Container) *dagger.Container {
	complianceMetadata := map[string]interface{}{
		"frameworks": []map[string]interface{}{
			{
				"name":         "SLSA",
				"version":      "v1.0",
				"level":        wkm.compliance.SLSALevel,
				"status":       "compliant",
				"last_audit":   time.Now().AddDate(0, -1, 0).Format("2006-01-02"),
				"next_audit":   time.Now().AddDate(0, 11, 0).Format("2006-01-02"),
				"certificate":  fmt.Sprintf("%s/.well-known/certificates/slsa-level-5.pem", wkm.metadata.Website),
			},
			{
				"name":         "NIST Cybersecurity Framework",
				"version":      "v1.1",
				"status":       "compliant",
				"profile":      "Manufacturing",
				"last_audit":   time.Now().AddDate(0, -2, 0).Format("2006-01-02"),
				"next_audit":   time.Now().AddDate(0, 10, 0).Format("2006-01-02"),
				"report":       fmt.Sprintf("%s/.well-known/reports/nist-csf.pdf", wkm.metadata.Website),
			},
		},
		"certifications": wkm.metadata.CertificationsBadges,
		"audit_schedule": map[string]interface{}{
			"frequency":    "quarterly",
			"external":     true,
			"auditor":      "Third-party Security Auditor",
			"scope":        []string{"Infrastructure", "Applications", "Supply Chain"},
			"next_audit":   time.Now().AddDate(0, 3, 0).Format("2006-01-02"),
		},
		"governance": map[string]interface{}{
			"framework":        wkm.compliance.GovernanceFramework,
			"policy_engine":    "Open Policy Agent",
			"risk_framework":   "NIST RMF",
			"incident_response": fmt.Sprintf("%s/security/incident-response", wkm.metadata.Website),
		},
		"transparency": map[string]interface{}{
			"audit_logs":       fmt.Sprintf("%s/.well-known/audit-logs/", wkm.metadata.Website),
			"compliance_dashboard": fmt.Sprintf("%s/compliance/dashboard", wkm.metadata.Website),
			"public_reports":   fmt.Sprintf("%s/compliance/reports", wkm.metadata.Website),
		},
		"metadata": map[string]interface{}{
			"generated_at":     time.Now().Format(time.RFC3339),
			"mcstack_version":  "v9r0_enhanced",
			"contact":          wkm.metadata.ContactEmail,
		},
	}

	complianceJSON, _ := json.MarshalIndent(complianceMetadata, "", "  ")
	return container.WithNewFile(".well-known/compliance.json", string(complianceJSON))
}

// generateAPIDiscovery creates API discovery metadata
func (wkm *WellKnownModule) generateAPIDiscovery(container *dagger.Container) *dagger.Container {
	apiDiscovery := map[string]interface{}{
		"api_version": "v1",
		"endpoints": map[string]interface{}{
			"rest": map[string]interface{}{
				"base_url":     fmt.Sprintf("https://%s/api/v1", wkm.metadata.Domain),
				"openapi_spec": fmt.Sprintf("https://%s/.well-known/openapi.json", wkm.metadata.Domain),
				"documentation": fmt.Sprintf("https://%s/docs/api", wkm.metadata.Website),
				"authentication": []string{"Bearer", "API-Key", "mTLS"},
			},
			"grpc": map[string]interface{}{
				"endpoint":     fmt.Sprintf("%s:443", wkm.metadata.Domain),
				"reflection":   true,
				"proto_files":  fmt.Sprintf("https://%s/.well-known/proto/", wkm.metadata.Domain),
				"authentication": []string{"mTLS", "JWT"},
			},
			"graphql": map[string]interface{}{
				"endpoint":    fmt.Sprintf("https://%s/graphql", wkm.metadata.Domain),
				"schema":      fmt.Sprintf("https://%s/.well-known/graphql-schema.json", wkm.metadata.Domain),
				"playground":  fmt.Sprintf("https://%s/graphql/playground", wkm.metadata.Domain),
			},
		},
		"health": map[string]interface{}{
			"liveness":  fmt.Sprintf("https://%s/health", wkm.metadata.Domain),
			"readiness": fmt.Sprintf("https://%s/ready", wkm.metadata.Domain),
			"metrics":   fmt.Sprintf("https://%s/metrics", wkm.metadata.Domain),
		},
		"authentication": map[string]interface{}{
			"methods": []string{"OAuth 2.0", "OpenID Connect", "API Keys", "mTLS"},
			"oauth2":  fmt.Sprintf("https://%s/.well-known/oauth-authorization-server", wkm.metadata.Domain),
			"oidc":    fmt.Sprintf("https://%s/.well-known/openid_configuration", wkm.metadata.Domain),
		},
		"rate_limiting": map[string]interface{}{
			"default_rate": "1000/hour",
			"authenticated_rate": "10000/hour",
			"headers": []string{"X-RateLimit-Limit", "X-RateLimit-Remaining", "X-RateLimit-Reset"},
		},
		"security": map[string]interface{}{
			"tls_versions": []string{"1.2", "1.3"},
			"cipher_suites": []string{"TLS_AES_256_GCM_SHA384", "TLS_CHACHA20_POLY1305_SHA256"},
			"hsts": true,
			"post_quantum": true,
		},
	}

	apiJSON, _ := json.MarshalIndent(apiDiscovery, "", "  ")
	return container.WithNewFile(".well-known/api-discovery.json", string(apiJSON))
}

// generateSupportInfo creates support and contact information
func (wkm *WellKnownModule) generateSupportInfo(container *dagger.Container) *dagger.Container {
	supportInfo := map[string]interface{}{
		"organization": map[string]interface{}{
			"name":           wkm.metadata.Name,
			"website":        wkm.metadata.Website,
			"description":    wkm.metadata.Description,
			"founded":        wkm.metadata.Founded.Format("2006-01-02"),
			"headquarters":   wkm.metadata.Headquarters,
			"industry":       wkm.metadata.Industry,
		},
		"contact": map[string]interface{}{
			"general":  wkm.metadata.ContactEmail,
			"support":  wkm.metadata.SupportEmail,
			"security": wkm.metadata.SecurityEmail,
			"sales":    fmt.Sprintf("sales@%s", wkm.metadata.Domain),
			"press":    fmt.Sprintf("press@%s", wkm.metadata.Domain),
		},
		"support_channels": map[string]interface{}{
			"email":        wkm.metadata.SupportEmail,
			"documentation": fmt.Sprintf("%s/docs", wkm.metadata.Website),
			"community":    fmt.Sprintf("%s/community", wkm.metadata.Website),
			"status_page":  fmt.Sprintf("https://status.%s", wkm.metadata.Domain),
			"chat":         fmt.Sprintf("%s/chat", wkm.metadata.Website),
		},
		"business_hours": map[string]interface{}{
			"timezone":      "UTC",
			"support_hours": "24/7 for critical issues, 9-17 UTC for general support",
			"response_sla": map[string]string{
				"critical": "1 hour",
				"high":     "4 hours",
				"medium":   "24 hours",
				"low":      "72 hours",
			},
		},
		"social_media": map[string]string{
			"twitter":  "@mcstack",
			"linkedin": "company/mcstack",
			"github":   "github.com/mcstack",
		},
		"legal": map[string]interface{}{
			"terms_of_service": fmt.Sprintf("%s/legal/terms", wkm.metadata.Website),
			"privacy_policy":   fmt.Sprintf("%s/legal/privacy", wkm.metadata.Website),
			"cookie_policy":    fmt.Sprintf("%s/legal/cookies", wkm.metadata.Website),
			"data_protection":  fmt.Sprintf("%s/legal/data-protection", wkm.metadata.Website),
		},
	}

	supportJSON, _ := json.MarshalIndent(supportInfo, "", "  ")
	return container.WithNewFile(".well-known/support.json", string(supportJSON))
}

// generateHumansTxt creates humans.txt file
func (wkm *WellKnownModule) generateHumansTxt(container *dagger.Container) *dagger.Container {
	humansTxt := fmt.Sprintf(`/* TEAM */

Security Engineer: MCStack Security Team
Contact: %s
Location: Global

DevOps Engineer: MCStack DevOps Team
Contact: %s
Location: Global

/* THANKS */

Thanks to the open source community for making this possible.
Special thanks to the SLSA, Sigstore, and CNCF communities.

/* SITE */

Last update: %s
Language: English
Doctype: HTML5
IDE: VS Code, GoLand
Framework: Dagger, Go, React
Security: SLSA Level 5, Post-Quantum Cryptography
Standards: NIST, OWASP, SANS

/* TECHNOLOGY */

Go: https://golang.org/
Dagger: https://dagger.io/
Kubernetes: https://kubernetes.io/
Sigstore: https://sigstore.dev/
SLSA: https://slsa.dev/

/* MCStack v9r0 Enhanced */
The future of secure software supply chains.
`,
		wkm.metadata.SecurityEmail,
		wkm.metadata.ContactEmail,
		time.Now().Format("2006-01-02"),
	)

	return container.WithNewFile(".well-known/humans.txt", humansTxt)
}

// generateHealthConfig creates health check configuration
func (wkm *WellKnownModule) generateHealthConfig(container *dagger.Container) *dagger.Container {
	healthConfig := map[string]interface{}{
		"endpoints": map[string]interface{}{
			"liveness": map[string]interface{}{
				"path":        "/health",
				"method":      "GET",
				"description": "Service liveness check",
				"response":    map[string]string{"status": "ok"},
			},
			"readiness": map[string]interface{}{
				"path":        "/ready",
				"method":      "GET",
				"description": "Service readiness check",
				"dependencies": []string{"database", "redis", "external_api"},
			},
			"metrics": map[string]interface{}{
				"path":        "/metrics",
				"method":      "GET",
				"format":      "prometheus",
				"description": "Application metrics",
			},
		},
		"monitoring": map[string]interface{}{
			"check_interval": "30s",
			"timeout":        "5s",
			"failure_threshold": 3,
			"success_threshold": 1,
		},
		"status_codes": map[string]interface{}{
			"healthy":   200,
			"unhealthy": 503,
			"degraded":  200,
		},
		"metadata": map[string]interface{}{
			"version":          "v1.0.0",
			"mcstack_version":  "v9r0_enhanced",
			"generated_at":     time.Now().Format(time.RFC3339),
		},
	}

	healthJSON, _ := json.MarshalIndent(healthConfig, "", "  ")
	return container.WithNewFile(".well-known/health.json", string(healthJSON))
}

// GetWellKnownMetrics returns .well-known generation metrics
func (wkm *WellKnownModule) GetWellKnownMetrics() map[string]interface{} {
	var enabledFiles []string
	if wkm.config.SecurityTxtEnabled { enabledFiles = append(enabledFiles, "security.txt") }
	if wkm.config.SLSAProvenanceEnabled { enabledFiles = append(enabledFiles, "slsa-provenance.json") }
	if wkm.config.SBOMMetadataEnabled { enabledFiles = append(enabledFiles, "sbom.json") }
	if wkm.config.PQCAnnouncementEnabled { enabledFiles = append(enabledFiles, "pqc-announcement.json") }
	if wkm.config.ComplianceFrameworkEnabled { enabledFiles = append(enabledFiles, "compliance.json") }
	if wkm.config.APIDiscoveryEnabled { enabledFiles = append(enabledFiles, "api-discovery.json") }

	return map[string]interface{}{
		"wellknown.files_generated":     enabledFiles,
		"wellknown.files_count":         len(enabledFiles),
		"wellknown.security_enabled":    wkm.config.SecurityTxtEnabled,
		"wellknown.slsa_enabled":        wkm.config.SLSAProvenanceEnabled,
		"wellknown.pqc_enabled":         wkm.config.PQCAnnouncementEnabled,
		"wellknown.compliance_enabled":  wkm.config.ComplianceFrameworkEnabled,
		"wellknown.api_discovery":       wkm.config.APIDiscoveryEnabled,
		"wellknown.slsa_level":          wkm.compliance.SLSALevel,
	}
}

// Outstanding UX: Provide clear .well-known generation status
func (wkm *WellKnownModule) GetWellKnownStatus() string {
	fileCount := 0
	if wkm.config.SecurityTxtEnabled { fileCount++ }
	if wkm.config.SLSAProvenanceEnabled { fileCount++ }
	if wkm.config.SBOMMetadataEnabled { fileCount++ }
	if wkm.config.PQCAnnouncementEnabled { fileCount++ }
	if wkm.config.ComplianceFrameworkEnabled { fileCount++ }
	if wkm.config.APIDiscoveryEnabled { fileCount++ }

	return fmt.Sprintf("📋 .well-known Generation ready: %d files | SLSA Level %d | Security: %s | Compliance: %s",
		fileCount,
		wkm.compliance.SLSALevel,
		func() string {
			if wkm.config.SecurityTxtEnabled && wkm.config.PQCAnnouncementEnabled {
				return "Quantum-Safe"
			}
			return "Standard"
		}(),
		func() string {
			if wkm.config.ComplianceFrameworkEnabled && wkm.config.OSCALMetadataEnabled {
				return "Full"
			}
			return "Basic"
		}())
}